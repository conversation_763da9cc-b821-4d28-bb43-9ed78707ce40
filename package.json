{"name": "tollama", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3", "@tauri-apps/cli": "^2"}}