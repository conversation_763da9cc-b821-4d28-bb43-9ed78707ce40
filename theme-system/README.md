# Theme System Implementation

## Setup Steps:

1. Install dependencies:
```bash
npm install zustand clsx tailwind-merge @tanstack/react-query
```

2. Add ThemeScript to your HTML head (before any content renders):
```tsx
import { ThemeScript } from './theme-system/components/theme-script'

// In your root layout/document
<head>
  <ThemeScript />
</head>
```

3. Wrap your app with ThemeProvider:
```tsx
import { ThemeProvider } from './theme-system/components/theme-provider'

function App() {
  return (
    <ThemeProvider>
      {/* Your app content */}
    </ThemeProvider>
  )
}
```

4. Use theme controls:
```tsx
import { useThemeStore } from './theme-system/lib/theme-store'
import { toggleThemeMode } from './theme-system/lib/toggle-theme-mode'

function ThemeToggle() {
  const { themeState } = useThemeStore()
  
  return (
    <button onClick={toggleThemeMode}>
      {themeState.currentMode === 'dark' ? '🌙' : '☀️'}
    </button>
  )
}
```

## Key Features:
- Persistent theme storage with Zustand
- Smooth theme transitions with View Transitions API
- Support for custom theme URLs
- SSR-safe theme initialization
- Tailwind CSS integration