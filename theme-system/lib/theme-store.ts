import { create } from "zustand"
import { persist } from "zustand/middleware"

export const THEME_STORE_KEY = "theme-store"

type ThemeMode = "dark" | "light"

type ThemeState = {
    currentMode: ThemeMode
    cssVars: {
        theme: Record<string, string>
        light: Record<string, string>
        dark: Record<string, string>
    }
}

type ThemeStore = {
    themeState: ThemeState
    setThemeState: (themeState: ThemeState) => void
}

const DEFAULT_THEME = {
    themeState: {
        currentMode: "light" as ThemeMode,
        cssVars: {
            theme: {
                background: "0 0% 100%",
                foreground: "222.2 84% 4.9%",
                primary: "222.2 47.4% 11.2%",
                "primary-foreground": "210 40% 98%",
                // Add your default theme variables here
            },
            light: {
                background: "0 0% 100%",
                foreground: "222.2 84% 4.9%",
                // Light mode specific variables
            },
            dark: {
                background: "222.2 84% 4.9%",
                foreground: "210 40% 98%",
                // Dark mode specific variables
            }
        }
    }
}

export const useThemeStore = create<ThemeStore>()(
    persist(
        (set) => ({
            themeState: DEFAULT_THEME.themeState,
            setThemeState: (themeState) => set({ themeState })
        }),
        {
            name: THEME_STORE_KEY,
            partialize: (state) => ({ themeState: state.themeState })
        }
    )
)