export const THEME_URLS = [
    "https://tweakcn.com/themes/cmc335y45000n04ld51zg72j3",
    "https://tweakcn.com/editor/theme?theme=mono",
    "https://tweakcn.com/editor/theme?theme=t3-chat",
    // Add more theme URLs as needed
]

export type ThemePreset = {
    cssVars: {
        theme: Record<string, string>
        light: Record<string, string>
        dark: Record<string, string>
    }
}

export type FetchedTheme = {
    name: string
    preset: ThemePreset
    url: string
    error?: string
    type: "custom" | "built-in"
}

export function convertToThemePreset(externalTheme: any): ThemePreset {
    if (externalTheme.cssVars) {
        return {
            cssVars: {
                theme: externalTheme.cssVars.theme || {},
                light: externalTheme.cssVars.light || {},
                dark: externalTheme.cssVars.dark || {}
            }
        }
    }
    throw new Error("Unsupported theme format")
}

export function getThemeName(themeData: any, url: string): string {
    return themeData.name || themeData.title || `Theme from ${new URL(url).hostname}`
}

export function extractThemeColors(preset: ThemePreset, mode: "light" | "dark"): string[] {
    const vars = { ...preset.cssVars.theme, ...preset.cssVars[mode] }
    const colorKeys = ["primary", "secondary", "accent", "background", "foreground"]
    
    return colorKeys
        .map(key => vars[key])
        .filter(Boolean)
        .map(value => `hsl(${value})`)
        .slice(0, 5)
}

export async function fetchThemeFromUrl(url: string): Promise<FetchedTheme> {
    try {
        const response = await fetch(url)
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        const themeData = await response.json()
        const themePreset = convertToThemePreset(themeData)
        const themeName = getThemeName(themeData, url)
        
        return {
            name: themeName,
            preset: themePreset,
            url,
            type: THEME_URLS.includes(url) ? "built-in" : "custom"
        }
    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch theme"
        return {
            name: getThemeName({}, url),
            preset: { cssVars: { theme: {}, light: {}, dark: {} } },
            url,
            error: errorMessage,
            type: THEME_URLS.includes(url) ? "built-in" : "custom"
        }
    }
}