export function ThemeScript() {
    const scriptContent = `
    (function() {
      const storageKey = "theme-store";
      const root = document.documentElement;

      let themeState = null;
      try {
        const persistedStateJSON = localStorage.getItem(storageKey);
        if (persistedStateJSON) {
          themeState = JSON.parse(persistedStateJSON)?.state?.themeState;
        }
      } catch (e) {
        console.warn("Theme initialization: Failed to read/parse localStorage:", e);
      }

      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
      const mode = themeState?.currentMode ?? (prefersDark ? "dark" : "light");

      const activeStyles = mode === "dark" ? themeState?.cssVars?.dark : themeState?.cssVars?.light;

      if (activeStyles) {
        Object.entries(activeStyles).forEach(([key, value]) => {
          root.style.setProperty(\`--\${key}\`, value);
        });
      }

      root.setAttribute("data-theme", mode);
      root.classList.toggle("dark", mode === "dark");
      root.classList.toggle("light", mode === "light");
    })();
    `

    return <script dangerouslySetInnerHTML={{ __html: scriptContent }} />
}
