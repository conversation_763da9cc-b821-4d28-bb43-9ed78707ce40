import { But<PERSON> } from "../ui/button"
import { useThemeStore } from "../../lib/theme-store"
import { toggleThemeMode } from "../../lib/toggle-theme-mode"
import { useChatStore } from "../../lib/chat-store"
import { 
  Sun, 
  Moon, 
  Plus, 
  MessageSquare, 
  Trash2,
  Settings
} from "lucide-react"

export function ChatHeader() {
  const { themeState } = useThemeStore()
  const { 
    getCurrentConversation, 
    createConversation, 
    deleteConversation,
    conversations,
    currentConversationId 
  } = useChatStore()
  
  const currentConversation = getCurrentConversation()
  const isDark = themeState.currentMode === "dark"

  const handleNewChat = () => {
    createConversation()
  }

  const handleDeleteChat = () => {
    if (currentConversationId && conversations.length > 1) {
      deleteConversation(currentConversationId)
    }
  }

  return (
    <header className="chat-header">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5 text-primary" />
          <h1 className="text-lg font-semibold">
            {currentConversation?.title || "Chat"}
          </h1>
        </div>
        
        <div className="text-sm text-muted-foreground">
          {conversations.length} conversation{conversations.length !== 1 ? 's' : ''}
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleNewChat}
          title="New Chat"
        >
          <Plus className="w-4 h-4" />
        </Button>

        {currentConversationId && conversations.length > 1 && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDeleteChat}
            title="Delete Chat"
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        )}

        <div className="w-px h-6 bg-border mx-1" />

        <Button
          variant="ghost"
          size="icon"
          onClick={toggleThemeMode}
          title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        >
          {isDark ? (
            <Sun className="w-4 h-4" />
          ) : (
            <Moon className="w-4 h-4" />
          )}
        </Button>

        <Button
          variant="ghost"
          size="icon"
          title="Settings"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </header>
  )
}
