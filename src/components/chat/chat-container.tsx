import { useEffect, useRef } from "react"
import { useChatStore } from "../../lib/chat-store"
import { ChatMessage } from "./chat-message"
import { MessageInput } from "./message-input"
import { ChatHeader } from "./chat-header"
import { MessageSquare } from "lucide-react"

export function ChatContainer() {
  const { 
    getCurrentConversation, 
    addMessage, 
    createConversation,
    isLoading,
    setLoading,
    conversations,
    currentConversationId
  } = useChatStore()
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const currentConversation = getCurrentConversation()

  // Create initial conversation if none exists
  useEffect(() => {
    if (conversations.length === 0) {
      createConversation("Welcome Chat")
    }
  }, [conversations.length, createConversation])

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [currentConversation?.messages])

  const handleSendMessage = async (content: string) => {
    if (!currentConversationId) return

    // Add user message
    addMessage(currentConversationId, content, "user")
    setLoading(true)

    try {
      // Simulate AI response (replace with actual AI integration)
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
      
      const responses = [
        "I understand your message. How can I help you further?",
        "That's an interesting point. Let me think about that...",
        "I see what you mean. Here's my perspective on that:",
        "Thanks for sharing that with me. I'd be happy to discuss this topic.",
        "That's a great question! Let me provide you with some insights.",
        "I appreciate you bringing this up. Here's what I think:",
      ]
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      addMessage(currentConversationId, randomResponse, "assistant")
    } catch (error) {
      console.error("Error sending message:", error)
      addMessage(currentConversationId, "Sorry, I encountered an error. Please try again.", "assistant")
    } finally {
      setLoading(false)
    }
  }

  if (!currentConversation) {
    return (
      <div className="chat-container">
        <ChatHeader />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Loading chat...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="chat-container">
      <ChatHeader />
      
      <div className="chat-messages">
        {currentConversation.messages.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-muted-foreground max-w-md">
              <MessageSquare className="w-16 h-16 mx-auto mb-6 opacity-50" />
              <h2 className="text-xl font-semibold mb-2">Start a conversation</h2>
              <p className="text-sm">
                Send a message to begin chatting. This is a local chat application 
                with a modern, clean interface.
              </p>
            </div>
          </div>
        ) : (
          <>
            {currentConversation.messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      <MessageInput 
        onSendMessage={handleSendMessage}
        isLoading={isLoading}
        placeholder="Type your message..."
      />
    </div>
  )
}
