import { Message } from "../../lib/chat-store"
import { cn } from "../../lib/utils"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"

interface ChatMessageProps {
  message: Message
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === "user"
  
  return (
    <div className={cn(
      "flex gap-3 max-w-4xl mx-auto animate-slide-up",
      isUser ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Avatar */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        isUser 
          ? "bg-primary text-primary-foreground" 
          : "bg-muted text-muted-foreground"
      )}>
        {isUser ? (
          <User className="w-4 h-4" />
        ) : (
          <Bot className="w-4 h-4" />
        )}
      </div>

      {/* Message content */}
      <div className={cn(
        "flex flex-col gap-1 max-w-[70%]",
        isUser ? "items-end" : "items-start"
      )}>
        <div className={cn(
          "message-bubble",
          isUser ? "message-user" : "message-assistant"
        )}>
          <p className="whitespace-pre-wrap break-words">
            {message.content}
          </p>
        </div>
        
        <span className="message-timestamp">
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </span>
      </div>
    </div>
  )
}
