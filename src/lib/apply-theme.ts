type ThemeMode = "dark" | "light"

type ThemeState = {
    currentMode: ThemeMode
    cssVars: {
        theme: Record<string, string>
        light: Record<string, string>
        dark: Record<string, string>
    }
}

export function applyThemeToElement(themeState: ThemeState, element: HTMLElement) {
    if (!element) return

    // Apply base theme variables
    Object.entries(themeState.cssVars.theme).forEach(([key, value]) => {
        element.style.setProperty(`--${key}`, value)
    })

    // Apply mode-specific variables
    const modeVars = themeState.cssVars[themeState.currentMode]
    Object.entries(modeVars).forEach(([key, value]) => {
        element.style.setProperty(`--${key}`, value)
    })

    // Update data attribute and classes for CSS targeting
    element.setAttribute("data-theme", themeState.currentMode)
    element.classList.toggle("dark", themeState.currentMode === "dark")
    element.classList.toggle("light", themeState.currentMode === "light")
}
