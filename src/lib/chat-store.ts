import { create } from "zustand"
import { persist } from "zustand/middleware"

export type Message = {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

export type Conversation = {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
}

type ChatState = {
  conversations: Conversation[]
  currentConversationId: string | null
  isLoading: boolean
}

type ChatActions = {
  // Conversation management
  createConversation: (title?: string) => string
  deleteConversation: (id: string) => void
  setCurrentConversation: (id: string) => void
  updateConversationTitle: (id: string, title: string) => void
  
  // Message management
  addMessage: (conversationId: string, content: string, role: "user" | "assistant") => void
  clearMessages: (conversationId: string) => void
  
  // UI state
  setLoading: (loading: boolean) => void
  
  // Getters
  getCurrentConversation: () => Conversation | null
  getConversation: (id: string) => Conversation | null
}

type ChatStore = ChatState & ChatActions

const generateId = () => Math.random().toString(36).substring(2, 15)

const createDefaultConversation = (): Conversation => ({
  id: generateId(),
  title: "New Chat",
  messages: [],
  createdAt: new Date(),
  updatedAt: new Date(),
})

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      // Initial state
      conversations: [],
      currentConversationId: null,
      isLoading: false,

      // Conversation management
      createConversation: (title = "New Chat") => {
        const newConversation: Conversation = {
          id: generateId(),
          title,
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        }
        
        set((state) => ({
          conversations: [newConversation, ...state.conversations],
          currentConversationId: newConversation.id,
        }))
        
        return newConversation.id
      },

      deleteConversation: (id) => {
        set((state) => {
          const filteredConversations = state.conversations.filter(c => c.id !== id)
          const newCurrentId = state.currentConversationId === id 
            ? (filteredConversations[0]?.id || null)
            : state.currentConversationId
          
          return {
            conversations: filteredConversations,
            currentConversationId: newCurrentId,
          }
        })
      },

      setCurrentConversation: (id) => {
        set({ currentConversationId: id })
      },

      updateConversationTitle: (id, title) => {
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === id 
              ? { ...conv, title, updatedAt: new Date() }
              : conv
          )
        }))
      },

      // Message management
      addMessage: (conversationId, content, role) => {
        const newMessage: Message = {
          id: generateId(),
          content,
          role,
          timestamp: new Date(),
        }

        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? {
                  ...conv,
                  messages: [...conv.messages, newMessage],
                  updatedAt: new Date(),
                  // Auto-update title for first user message
                  title: conv.messages.length === 0 && role === "user" 
                    ? content.slice(0, 50) + (content.length > 50 ? "..." : "")
                    : conv.title
                }
              : conv
          )
        }))
      },

      clearMessages: (conversationId) => {
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === conversationId
              ? { ...conv, messages: [], updatedAt: new Date() }
              : conv
          )
        }))
      },

      // UI state
      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      // Getters
      getCurrentConversation: () => {
        const state = get()
        return state.conversations.find(c => c.id === state.currentConversationId) || null
      },

      getConversation: (id) => {
        const state = get()
        return state.conversations.find(c => c.id === id) || null
      },
    }),
    {
      name: "chat-store",
      partialize: (state) => ({
        conversations: state.conversations,
        currentConversationId: state.currentConversationId,
      }),
    }
  )
)
